import { useState, useEffect } from 'react';
import { Link } from '@inertiajs/react';
import { motion, AnimatePresence } from 'framer-motion';
import {
    Menu,
    X,
    ChevronRight,
    Users,
    BookOpen,
    Trophy,
    Calendar,
    MapPin,
    Phone,
    Mail,
    Star,
    ArrowUp,
    Play,
    Award,
    GraduationCap,
    Target,
    Heart
} from 'lucide-react';

export default function SchoolLandingPage() {
    const [menuOpen, setMenuOpen] = useState(false);
    const [currentSlide, setCurrentSlide] = useState(0);
    const [showScrollTop, setShowScrollTop] = useState(false);

    const heroSlides = [
        {
            id: 1,
            image: 'https://source.unsplash.com/1600x900/?school,classroom',
            title: 'Selamat Datang di SMA Negeri Contoh',
            subtitle: 'Sekolah unggul dalam prestasi, karakter, dan teknologi'
        },
        {
            id: 2,
            image: 'https://source.unsplash.com/1600x900/?students,learning',
            title: 'Pendidikan Berkualitas untuk Masa <PERSON>pan',
            subtitle: 'Membangun generasi cerdas dan berkarakter'
        },
        {
            id: 3,
            image: 'https://source.unsplash.com/1600x900/?laboratory,science',
            title: 'Fasilitas Modern dan Lengkap',
            subtitle: 'Mendukung pembelajaran yang optimal'
        }
    ];

    const [berita] = useState([
        {
            id: 1,
            judul: 'Juara 1 Lomba Sains Nasional 2025',
            tanggal: '01 Juli 2025',
            gambar: 'https://source.unsplash.com/600x400/?science,student',
            excerpt: 'Tim sains SMA Negeri Contoh berhasil meraih juara 1 dalam kompetisi sains tingkat nasional.',
            kategori: 'Prestasi'
        },
        {
            id: 2,
            judul: 'Kunjungan Industri ke PT Telkom Indonesia',
            tanggal: '28 Juni 2025',
            gambar: 'https://source.unsplash.com/600x400/?technology,visit',
            excerpt: 'Siswa kelas XI mengunjungi PT Telkom untuk mempelajari teknologi terkini.',
            kategori: 'Kegiatan'
        },
        {
            id: 3,
            judul: 'Pembukaan Tahun Ajaran Baru 2025/2026',
            tanggal: '15 Juli 2025',
            gambar: 'https://source.unsplash.com/600x400/?school,ceremony',
            excerpt: 'Upacara pembukaan tahun ajaran baru dengan tema "Bersama Membangun Masa Depan".',
            kategori: 'Acara'
        }
    ]);

    const [prestasi] = useState([
        {
            id: 1,
            nama: 'Dina Aulia Putri',
            jenis: 'Olimpiade Matematika Nasional',
            tahun: '2025',
            tingkat: 'Nasional',
            peringkat: 'Juara 1'
        },
        {
            id: 2,
            nama: 'Rafi Hidayat',
            jenis: 'Lomba Robotik Tingkat Provinsi',
            tahun: '2025',
            tingkat: 'Provinsi',
            peringkat: 'Juara 2'
        },
        {
            id: 3,
            nama: 'Sarah Amelia',
            jenis: 'Kompetisi Bahasa Inggris',
            tahun: '2024',
            tingkat: 'Regional',
            peringkat: 'Juara 1'
        }
    ]);

    const [modul] = useState([
        { id: 1, nama: 'Matematika Kelas 10', mata_pelajaran: 'Matematika', kelas: 'X', link: '#' },
        { id: 2, nama: 'Fisika Kelas 11', mata_pelajaran: 'Fisika', kelas: 'XI', link: '#' },
        { id: 3, nama: 'Kimia Kelas 12', mata_pelajaran: 'Kimia', kelas: 'XII', link: '#' },
        { id: 4, nama: 'Biologi Kelas 10', mata_pelajaran: 'Biologi', kelas: 'X', link: '#' }
    ]);

    const stats = [
        { icon: Users, label: 'Total Siswa', value: '1,250', color: 'text-blue-600' },
        { icon: GraduationCap, label: 'Guru Berpengalaman', value: '85', color: 'text-green-600' },
        { icon: Trophy, label: 'Prestasi Tahun Ini', value: '45', color: 'text-yellow-600' },
        { icon: BookOpen, label: 'Program Studi', value: '3', color: 'text-purple-600' }
    ];

    const facilities = [
        { name: 'Laboratorium Sains', icon: '🔬', description: 'Lab modern untuk praktikum fisika, kimia, dan biologi' },
        { name: 'Perpustakaan Digital', icon: '📚', description: 'Koleksi buku digital dan ruang baca yang nyaman' },
        { name: 'Lapangan Olahraga', icon: '⚽', description: 'Lapangan basket, voli, dan sepak bola' },
        { name: 'Ruang Multimedia', icon: '💻', description: 'Fasilitas komputer dan teknologi terkini' }
    ];

    // Auto slide hero
    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
        }, 5000);
        return () => clearInterval(interval);
    }, [heroSlides.length]);

    // Scroll to top button
    useEffect(() => {
        const handleScroll = () => {
            setShowScrollTop(window.scrollY > 300);
        };
        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    const scrollToTop = () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    return (
        <div className="min-h-screen bg-white text-gray-800 dark:bg-gray-900 dark:text-white">
            {/* Header */}
            <header className="sticky top-0 z-50 bg-white/95 backdrop-blur-sm shadow-lg dark:bg-gray-900/95 transition-all duration-300">
                <div className="mx-auto flex max-w-7xl items-center justify-between px-6 py-4">
                    <motion.div
                        className="flex items-center gap-3"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5 }}
                    >
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold">
                            S
                        </div>
                        <div>
                            <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                SMA Negeri Contoh
                            </h1>
                            <p className="text-xs text-gray-500 dark:text-gray-400">Excellence in Education</p>
                        </div>
                    </motion.div>

                    <div className="lg:hidden">
                        <button
                            onClick={() => setMenuOpen(!menuOpen)}
                            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                        >
                            {menuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
                        </button>
                    </div>

                    <nav className="hidden lg:flex items-center gap-8 text-sm font-medium">
                        <a href="#tentang" className="hover:text-blue-600 transition-colors relative group">
                            Tentang
                            <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all group-hover:w-full"></span>
                        </a>
                        <a href="#berita" className="hover:text-blue-600 transition-colors relative group">
                            Berita
                            <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all group-hover:w-full"></span>
                        </a>
                        <a href="#fasilitas" className="hover:text-blue-600 transition-colors relative group">
                            Fasilitas
                            <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all group-hover:w-full"></span>
                        </a>
                        <a href="#prestasi" className="hover:text-blue-600 transition-colors relative group">
                            Prestasi
                            <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all group-hover:w-full"></span>
                        </a>
                        <a href="#kontak" className="hover:text-blue-600 transition-colors relative group">
                            Kontak
                            <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all group-hover:w-full"></span>
                        </a>
                        <Link
                            href="/login"
                            className="rounded-full bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-2 text-white hover:shadow-lg transform hover:scale-105 transition-all duration-200"
                        >
                            Login
                        </Link>
                    </nav>
                </div>

                <AnimatePresence>
                    {menuOpen && (
                        <motion.div
                            className="lg:hidden border-t dark:border-gray-700"
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3 }}
                        >
                            <div className="px-6 py-4 space-y-3">
                                <a href="#tentang" className="block py-2 hover:text-blue-600 transition-colors">Tentang</a>
                                <a href="#berita" className="block py-2 hover:text-blue-600 transition-colors">Berita</a>
                                <a href="#fasilitas" className="block py-2 hover:text-blue-600 transition-colors">Fasilitas</a>
                                <a href="#prestasi" className="block py-2 hover:text-blue-600 transition-colors">Prestasi</a>
                                <a href="#kontak" className="block py-2 hover:text-blue-600 transition-colors">Kontak</a>
                                <Link
                                    href="/login"
                                    className="block mt-4 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-2 text-white text-center hover:shadow-lg transition-all duration-200"
                                >
                                    Login
                                </Link>
                            </div>
                        </motion.div>
                    )}
                </AnimatePresence>
            </header>

            {/* Hero Section with Interactive Slider */}
            <section className="relative h-screen overflow-hidden">
                <AnimatePresence mode="wait">
                    <motion.div
                        key={currentSlide}
                        className="absolute inset-0 z-0 bg-cover bg-center"
                        style={{ backgroundImage: `url(${heroSlides[currentSlide].image})` }}
                        initial={{ scale: 1.1, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0.9, opacity: 0 }}
                        transition={{ duration: 1 }}
                    />
                </AnimatePresence>

                <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-transparent z-10" />

                <div className="relative z-20 flex h-full w-full items-center px-4">
                    <div className="mx-auto max-w-7xl w-full">
                        <div className="max-w-3xl">
                            <motion.div
                                key={`content-${currentSlide}`}
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.2 }}
                                className="text-white"
                            >
                                <h2 className="text-4xl sm:text-5xl md:text-6xl font-bold leading-tight mb-6">
                                    {heroSlides[currentSlide].title}
                                </h2>
                                <p className="text-lg sm:text-xl md:text-2xl mb-8 text-gray-200">
                                    {heroSlides[currentSlide].subtitle}
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4">
                                    <motion.button
                                        className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 flex items-center justify-center gap-2"
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                    >
                                        <Play className="w-5 h-5" />
                                        Jelajahi Sekolah
                                    </motion.button>
                                    <motion.a
                                        href="#tentang"
                                        className="border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-4 rounded-full font-semibold text-lg transition-all duration-200 flex items-center justify-center gap-2"
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                    >
                                        Pelajari Lebih Lanjut
                                        <ChevronRight className="w-5 h-5" />
                                    </motion.a>
                                </div>
                            </motion.div>
                        </div>
                    </div>
                </div>

                {/* Slide Indicators */}
                <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30 flex gap-3">
                    {heroSlides.map((_, index) => (
                        <button
                            key={index}
                            onClick={() => setCurrentSlide(index)}
                            className={`w-3 h-3 rounded-full transition-all duration-300 ${
                                index === currentSlide
                                    ? 'bg-white scale-125'
                                    : 'bg-white/50 hover:bg-white/75'
                            }`}
                        />
                    ))}
                </div>

                {/* Scroll Down Indicator */}
                <motion.div
                    className="absolute bottom-8 right-8 z-30 text-white"
                    animate={{ y: [0, 10, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                >
                    <div className="flex flex-col items-center gap-2">
                        <span className="text-sm font-medium">Scroll</span>
                        <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
                            <div className="w-1 h-3 bg-white rounded-full mt-2 animate-bounce"></div>
                        </div>
                    </div>
                </motion.div>
            </section>

            {/* Statistics Section */}
            <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                <div className="mx-auto max-w-7xl px-4">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                        {stats.map((stat, index) => (
                            <motion.div
                                key={index}
                                className="text-center"
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.5, delay: index * 0.1 }}
                                viewport={{ once: true }}
                            >
                                <stat.icon className="w-12 h-12 mx-auto mb-4 text-white/90" />
                                <motion.div
                                    className="text-3xl md:text-4xl font-bold mb-2"
                                    initial={{ scale: 0 }}
                                    whileInView={{ scale: 1 }}
                                    transition={{ duration: 0.5, delay: index * 0.1 + 0.2 }}
                                    viewport={{ once: true }}
                                >
                                    {stat.value}
                                </motion.div>
                                <p className="text-white/90 font-medium">{stat.label}</p>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </section>

            {/* About Section */}
            <section id="tentang" className="py-20 bg-gray-50 dark:bg-gray-800">
                <div className="mx-auto max-w-7xl px-4">
                    <div className="grid lg:grid-cols-2 gap-12 items-center">
                        <motion.div
                            initial={{ opacity: 0, x: -30 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.6 }}
                            viewport={{ once: true }}
                        >
                            <h3 className="text-3xl md:text-4xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                Tentang SMA Negeri Contoh
                            </h3>
                            <p className="text-lg text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                                SMA Negeri Contoh adalah institusi pendidikan yang berkomitmen untuk menghasilkan
                                generasi muda yang cerdas, berkarakter, dan siap menghadapi tantangan masa depan.
                                Dengan fasilitas modern dan tenaga pengajar berpengalaman, kami memberikan pendidikan
                                berkualitas tinggi yang mengintegrasikan akademik, karakter, dan teknologi.
                            </p>
                        </motion.div>
                        <motion.div
                            initial={{ opacity: 0, x: 30 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.6 }}
                            viewport={{ once: true }}
                            className="relative"
                        >
                            <div className="absolute-bottom-6 -left-6 bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg">
                                <div className="flex items-center gap-3">
                                    <Target className="w-6 h-6 text-blue-600" />
                                    <span className="font-semibold">Visi:</span>
                                    <span className="text-gray-600 dark:text-gray-300">Terwujudnya peserta didik yang bertakwa, cerdas, kreatif, Inovatif dan  berbudaya</span>
                                </div>
                                <div className="flex items-center gap-3">
                                    <Award className="w-6 h-6 text-yellow-500" />
                                    <div>
                                        <span className="font-semibold">Misi:</span>
                                        <strong className='block'>Bertakwa</strong>
                                        <p className="text-gray-600 dark:text-gray-300 mt-1">
                                        Terwujudnya kesadaran siswa dalam melaksanakan ibadah (sholat wajib, sholat dhuha, puasa, tadarus al-Qur’an) dan penerapan nilai-nilai ajaran agama dengan penuh rasa tanggung jawab.
                                        </p>
                                        <strong className='block'>Cerdas</strong>
                                        <p className="text-gray-600 dark:text-gray-300 mt-1">
                                        Terwujudnya generasi yang memiliki perkembangan akal budi, dan pola pikir yang lebih baik.
                                        </p>
                                        <strong className='block'>Kreatif</strong>
                                        <p className="text-gray-600 dark:text-gray-300 mt-1">
                                        Terwujudnya generasi yang memiliki pengetahuan dan keterampilan yang mampu menciptakan hasil karya di bidang teknologi informasi, seni dan budaya.
                                        </p>
                                        <strong className='block'>Inovatif</strong>
                                        <p className="text-gray-600 dark:text-gray-300 mt-1">
                                        Terwujutnya generasi yang memiliki pengetahuan dan keterampilan serta mampu menemukan karya-karya baru di bidang teknologi informasi, seni dan budaya.
                                        </p>
                                        <strong className='block'>Berbudaya</strong>
                                        <p className="text-gray-600 dark:text-gray-300 mt-1">
                                        Terwujudnya karakter siswa yang taat tata tertib, peduli lingkungan, cinta budaya bangsa.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </motion.div>
                    </div>
                </div>
            </section>

            {/* Facilities Section */}
            <section id="fasilitas" className="py-20">
                <div className="mx-auto max-w-7xl px-4">
                    <motion.div
                        className="text-center mb-16"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                        viewport={{ once: true }}
                    >
                        <h3 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            Fasilitas Unggulan
                        </h3>
                        <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                            Fasilitas modern dan lengkap untuk mendukung proses pembelajaran yang optimal
                        </p>
                    </motion.div>
                    <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                        {facilities.map((facility, index) => (
                            <motion.div
                                key={index}
                                className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 text-center group hover:scale-105"
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.5, delay: index * 0.1 }}
                                viewport={{ once: true }}
                            >
                                <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                                    {facility.icon}
                                </div>
                                <h4 className="font-bold text-lg mb-3 text-gray-800 dark:text-white">
                                    {facility.name}
                                </h4>
                                <p className="text-gray-600 dark:text-gray-300 text-sm">
                                    {facility.description}
                                </p>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Berita */}
            <section id="berita" className="py-20 bg-gray-50 dark:bg-gray-800">
                <div className="mx-auto max-w-7xl px-4">
                    <motion.div
                        className="text-center mb-16"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                        viewport={{ once: true }}
                    >
                        <h3 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            Berita & Kegiatan Terkini
                        </h3>
                        <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                            Ikuti perkembangan terbaru dan kegiatan menarik di sekolah kami
                        </p>
                    </motion.div>

                    <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                        {berita.map((item, index) => (
                            <motion.article
                                key={item.id}
                                className="bg-white dark:bg-gray-900 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 group"
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: index * 0.1 }}
                                viewport={{ once: true }}
                                whileHover={{ y: -5 }}
                            >
                                <div className="relative overflow-hidden">
                                    <img
                                        src={item.gambar}
                                        alt={item.judul}
                                        className="h-48 w-full object-cover group-hover:scale-110 transition-transform duration-500"
                                    />
                                    <div className="absolute top-4 left-4">
                                        <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-semibold">
                                            {item.kategori}
                                        </span>
                                    </div>
                                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                </div>

                                <div className="p-6">
                                    <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 mb-3">
                                        <Calendar className="w-4 h-4" />
                                        <span>{item.tanggal}</span>
                                    </div>

                                    <h4 className="text-xl font-bold mb-3 text-gray-800 dark:text-white group-hover:text-blue-600 transition-colors duration-300">
                                        {item.judul}
                                    </h4>

                                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-3">
                                        {item.excerpt}
                                    </p>

                                    <button className="flex items-center gap-2 text-blue-600 hover:text-blue-700 font-semibold text-sm group-hover:gap-3 transition-all duration-300">
                                        Baca Selengkapnya
                                        <ChevronRight className="w-4 h-4" />
                                    </button>
                                </div>
                            </motion.article>
                        ))}
                    </div>

                    <motion.div
                        className="text-center mt-12"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        transition={{ duration: 0.6, delay: 0.3 }}
                        viewport={{ once: true }}
                    >
                        <button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200">
                            Lihat Semua Berita
                        </button>
                    </motion.div>
                </div>
            </section>

            {/* Modul Pembelajaran */}
            <section id="modul" className="py-20">
                <div className="mx-auto max-w-7xl px-4">
                    <motion.div
                        className="text-center mb-16"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                        viewport={{ once: true }}
                    >
                        <h3 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            Modul Pembelajaran Digital
                        </h3>
                        <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                            Akses materi pembelajaran digital yang interaktif dan mudah dipahami
                        </p>
                    </motion.div>

                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                        {modul.map((item, index) => (
                            <motion.div
                                key={item.id}
                                className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group border border-gray-100 dark:border-gray-700 hover:border-blue-200 dark:hover:border-blue-600"
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.5, delay: index * 0.1 }}
                                viewport={{ once: true }}
                                whileHover={{ y: -5 }}
                            >
                                <div className="flex items-center justify-between mb-4">
                                    <div className="bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 p-3 rounded-lg">
                                        <BookOpen className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                                    </div>
                                    <span className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-1 rounded-full text-xs font-semibold">
                                        Kelas {item.kelas}
                                    </span>
                                </div>

                                <h4 className="font-bold text-lg mb-2 text-gray-800 dark:text-white group-hover:text-blue-600 transition-colors duration-300">
                                    {item.mata_pelajaran}
                                </h4>

                                <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                                    {item.nama}
                                </p>

                                <a
                                    href={item.link}
                                    className="flex items-center justify-between w-full bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 hover:from-blue-100 hover:to-purple-100 dark:hover:from-blue-800/30 dark:hover:to-purple-800/30 text-blue-600 dark:text-blue-400 px-4 py-2 rounded-lg font-semibold text-sm transition-all duration-300 group-hover:shadow-md"
                                >
                                    <span>Akses Modul</span>
                                    <ChevronRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                                </a>
                            </motion.div>
                        ))}
                    </div>

                    <motion.div
                        className="text-center mt-12"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        transition={{ duration: 0.6, delay: 0.3 }}
                        viewport={{ once: true }}
                    >
                        <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-2xl p-8">
                            <h4 className="text-xl font-bold mb-4 text-gray-800 dark:text-white">
                                Butuh Akses Lebih Lengkap?
                            </h4>
                            <p className="text-gray-600 dark:text-gray-300 mb-6">
                                Login untuk mengakses semua modul pembelajaran dan fitur interaktif lainnya
                            </p>
                            <Link
                                href="/login"
                                className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
                            >
                                <Users className="w-5 h-5" />
                                Login Sekarang
                            </Link>
                        </div>
                    </motion.div>
                </div>
            </section>

            {/* Prestasi */}
            <section id="prestasi" className="py-20 bg-gray-50 dark:bg-gray-800">
                <div className="mx-auto max-w-7xl px-4">
                    <motion.div
                        className="text-center mb-16"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                        viewport={{ once: true }}
                    >
                        <h3 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            Prestasi Membanggakan
                        </h3>
                        <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                            Kebanggaan kami atas pencapaian luar biasa siswa-siswi berprestasi
                        </p>
                    </motion.div>

                    <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                        {prestasi.map((item, index) => (
                            <motion.div
                                key={item.id}
                                className="bg-white dark:bg-gray-900 rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 group border border-gray-100 dark:border-gray-700"
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: index * 0.1 }}
                                viewport={{ once: true }}
                                whileHover={{ y: -5 }}
                            >
                                <div className="flex items-start gap-4">
                                    <div className="bg-gradient-to-r from-yellow-400 to-orange-500 p-3 rounded-full flex-shrink-0">
                                        <Trophy className="w-6 h-6 text-white" />
                                    </div>
                                    <div className="flex-1">
                                        <div className="flex items-center gap-2 mb-2">
                                            <span className="bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 px-2 py-1 rounded-full text-xs font-semibold">
                                                {item.tingkat}
                                            </span>
                                            <span className="text-gray-500 dark:text-gray-400 text-sm">
                                                {item.tahun}
                                            </span>
                                        </div>
                                        <h4 className="font-bold text-lg mb-2 text-gray-800 dark:text-white">
                                            {item.nama}
                                        </h4>
                                        <p className="text-gray-600 dark:text-gray-300 text-sm mb-3">
                                            {item.jenis}
                                        </p>
                                        <div className="flex items-center gap-2">
                                            <Star className="w-4 h-4 text-yellow-500 fill-current" />
                                            <span className="font-semibold text-yellow-600 dark:text-yellow-400">
                                                {item.peringkat}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </motion.div>
                        ))}
                    </div>

                    <motion.div
                        className="text-center mt-12"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        transition={{ duration: 0.6, delay: 0.3 }}
                        viewport={{ once: true }}
                    >
                        <button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200">
                            Lihat Semua Prestasi
                        </button>
                    </motion.div>
                </div>
            </section>

            {/* Contact Section */}
            <section id="kontak" className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                <div className="mx-auto max-w-7xl px-4">
                    <motion.div
                        className="text-center mb-16"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                        viewport={{ once: true }}
                    >
                        <h3 className="text-3xl md:text-4xl font-bold mb-4">
                            Hubungi Kami
                        </h3>
                        <p className="text-lg text-white/90 max-w-2xl mx-auto">
                            Kami siap membantu dan menjawab pertanyaan Anda
                        </p>
                    </motion.div>

                    <div className="grid md:grid-cols-3 gap-8">
                        <motion.div
                            className="text-center"
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.1 }}
                            viewport={{ once: true }}
                        >
                            <div className="bg-white/10 backdrop-blur-sm p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                                <MapPin className="w-8 h-8" />
                            </div>
                            <h4 className="font-bold text-lg mb-2">Alamat</h4>
                            <p className="text-white/90">
                                Jl. Pendidikan No. 123<br />
                                Jakarta Selatan, 12345
                            </p>
                        </motion.div>

                        <motion.div
                            className="text-center"
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.2 }}
                            viewport={{ once: true }}
                        >
                            <div className="bg-white/10 backdrop-blur-sm p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                                <Phone className="w-8 h-8" />
                            </div>
                            <h4 className="font-bold text-lg mb-2">Telepon</h4>
                            <p className="text-white/90">
                                (021) 1234-5678<br />
                                (021) 8765-4321
                            </p>
                        </motion.div>

                        <motion.div
                            className="text-center"
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.3 }}
                            viewport={{ once: true }}
                        >
                            <div className="bg-white/10 backdrop-blur-sm p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                                <Mail className="w-8 h-8" />
                            </div>
                            <h4 className="font-bold text-lg mb-2">Email</h4>
                            <p className="text-white/90">
                                <EMAIL><br />
                                <EMAIL>
                            </p>
                        </motion.div>
                    </div>
                </div>
            </section>

            {/* Footer */}
            <footer className="bg-gray-900 text-white py-12">
                <div className="mx-auto max-w-7xl px-4">
                    <div className="grid md:grid-cols-4 gap-8 mb-8">
                        <div>
                            <div className="flex items-center gap-3 mb-4">
                                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold">
                                    S
                                </div>
                                <div>
                                    <h3 className="font-bold text-lg">SMA Negeri Contoh</h3>
                                    <p className="text-sm text-gray-400">Excellence in Education</p>
                                </div>
                            </div>
                            <p className="text-gray-400 text-sm">
                                Membangun generasi cerdas dan berkarakter untuk masa depan yang lebih baik.
                            </p>
                        </div>

                        <div>
                            <h4 className="font-bold mb-4">Menu Utama</h4>
                            <ul className="space-y-2 text-sm text-gray-400">
                                <li><a href="#tentang" className="hover:text-white transition-colors">Tentang</a></li>
                                <li><a href="#berita" className="hover:text-white transition-colors">Berita</a></li>
                                <li><a href="#fasilitas" className="hover:text-white transition-colors">Fasilitas</a></li>
                                <li><a href="#prestasi" className="hover:text-white transition-colors">Prestasi</a></li>
                            </ul>
                        </div>

                        <div>
                            <h4 className="font-bold mb-4">Akademik</h4>
                            <ul className="space-y-2 text-sm text-gray-400">
                                <li><a href="#" className="hover:text-white transition-colors">Program IPA</a></li>
                                <li><a href="#" className="hover:text-white transition-colors">Program IPS</a></li>
                                <li><a href="#" className="hover:text-white transition-colors">Program Bahasa</a></li>
                                <li><a href="#modul" className="hover:text-white transition-colors">Modul Digital</a></li>
                            </ul>
                        </div>

                        <div>
                            <h4 className="font-bold mb-4">Kontak</h4>
                            <ul className="space-y-2 text-sm text-gray-400">
                                <li>Jl. Pendidikan No. 123</li>
                                <li>Jakarta Selatan, 12345</li>
                                <li>(021) 1234-5678</li>
                                <li><EMAIL></li>
                            </ul>
                        </div>
                    </div>

                    <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
                        <p className="text-gray-400 text-sm">
                            © 2025 SMA Negeri Contoh. All rights reserved.
                        </p>
                        <div className="flex gap-4 mt-4 md:mt-0">
                            <a href="#" className="text-gray-400 hover:text-white transition-colors">Privacy Policy</a>
                            <a href="#" className="text-gray-400 hover:text-white transition-colors">Terms of Service</a>
                        </div>
                    </div>
                </div>
            </footer>

            {/* Scroll to Top Button */}
            <AnimatePresence>
                {showScrollTop && (
                    <motion.button
                        onClick={scrollToTop}
                        className="fixed bottom-8 right-8 z-50 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200"
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0 }}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                    >
                        <ArrowUp className="w-6 h-6" />
                    </motion.button>
                )}
            </AnimatePresence>
        </div>
    );
}
